import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { 
  Sparkles, 
  Wand2, 
  Send, 
  Image as ImageIcon, 
  Upload,
  Twitter,
  Facebook,
  Instagram,
  Linkedin,
  MessageSquare,
  Clock,
  Calendar,
  Target,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2,
  RefreshCw,
  Eye,
  Edit3,
  Download
} from "lucide-react";
import { FaReddit } from "react-icons/fa";
import { useToast } from "@/hooks/use-toast";
import { useSocialMedia } from "@/hooks/useSocialMedia";
import { supabase } from "@/integrations/supabase/client";

const CreatePostPage = () => {
  const { toast } = useToast();
  const { connections, postToMultiplePlatforms, checkConnectionStatus } = useSocialMedia();
  
  // State management
  const [prompt, setPrompt] = useState("");
  const [selectedTone, setSelectedTone] = useState("professional");
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [includeImage, setIncludeImage] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [schedulePost, setSchedulePost] = useState(false);
  const [scheduledTime, setScheduledTime] = useState("");

  // Platform configurations
  const platforms = [
    { 
      id: "twitter", 
      name: "Twitter", 
      icon: Twitter, 
      color: "bg-sky-500 hover:bg-sky-600", 
      textColor: "text-sky-600",
      bgLight: "bg-sky-50",
      limit: 280 
    },
    { 
      id: "facebook", 
      name: "Facebook", 
      icon: Facebook, 
      color: "bg-blue-600 hover:bg-blue-700", 
      textColor: "text-blue-600",
      bgLight: "bg-blue-50",
      limit: 63206 
    },
    { 
      id: "instagram", 
      name: "Instagram", 
      icon: Instagram, 
      color: "bg-pink-500 hover:bg-pink-600", 
      textColor: "text-pink-600",
      bgLight: "bg-pink-50",
      limit: 2200 
    },
    { 
      id: "linkedin", 
      name: "LinkedIn", 
      icon: Linkedin, 
      color: "bg-blue-700 hover:bg-blue-800", 
      textColor: "text-blue-700",
      bgLight: "bg-blue-50",
      limit: 3000 
    },
    { 
      id: "reddit", 
      name: "Reddit", 
      icon: FaReddit, 
      color: "bg-orange-500 hover:bg-orange-600", 
      textColor: "text-orange-600",
      bgLight: "bg-orange-50",
      limit: 40000 
    },
  ];

  // Tone options
  const toneOptions = [
    { id: "professional", name: "Professional", emoji: "💼", description: "Formal and business-like" },
    { id: "casual", name: "Casual", emoji: "😊", description: "Friendly and relaxed" },
    { id: "enthusiastic", name: "Enthusiastic", emoji: "🚀", description: "Energetic and exciting" },
    { id: "informative", name: "Informative", emoji: "📚", description: "Educational and factual" },
    { id: "humorous", name: "Humorous", emoji: "😄", description: "Light-hearted and funny" },
    { id: "inspirational", name: "Inspirational", emoji: "✨", description: "Motivating and uplifting" },
  ];

  // Get connected platforms
  const connectedPlatforms = connections
    .filter(conn => conn.isConnected)
    .map(conn => conn.platform);

  useEffect(() => {
    checkConnectionStatus();
  }, []);

  // Platform selection handler
  const togglePlatform = (platformId: string) => {
    if (!connectedPlatforms.includes(platformId)) {
      toast({
        title: "Platform not connected",
        description: `Please connect your ${platformId} account in Settings first.`,
        variant: "destructive",
      });
      return;
    }

    setSelectedPlatforms(prev => 
      prev.includes(platformId) 
        ? prev.filter(p => p !== platformId)
        : [...prev, platformId]
    );
  };

  // Clean AI generated content
  const cleanAIGeneratedContent = (content: string): string => {
    let cleaned = content.trim();
    
    // Remove AI instruction phrases
    const instructionPatterns = [
      /^(Sure!|Here are|Here's|I'll help|Let me|I can help).*?(\n|:)/i,
      /^(Here are some|Here's some).*?ideas.*?(\n|:)/i,
      /^.*social media content ideas.*?(\n|:)/i,
    ];

    instructionPatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '').trim();
    });

    // If content has numbered list, extract ONLY the first item
    if (cleaned.match(/^\d+\./m)) {
      const lines = cleaned.split('\n');
      for (const line of lines) {
        if (line.match(/^1\./)) {
          cleaned = line.replace(/^1\.\s*/, '').trim();
          break;
        }
      }
    }

    // Remove markdown and formatting
    cleaned = cleaned.replace(/\*\*(Caption|Content):\*\*/gi, '').trim();
    cleaned = cleaned.replace(/^###.*$/gm, '').trim();
    cleaned = cleaned.replace(/^---.*$/gm, '').trim();
    cleaned = cleaned.replace(/\n{3,}/g, '\n\n').trim();

    return cleaned;
  };

  // Generate AI image
  const generateImage = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Please enter a prompt",
        description: "Describe what you want to create an image for",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingImage(true);
    try {
      const imagePrompt = `Create a professional social media image for: ${prompt}. Make it visually appealing, modern, and suitable for social media platforms.`;

      const { data, error } = await supabase.functions.invoke('generate-content', {
        body: { prompt: imagePrompt, type: 'image' }
      });

      if (error) throw error;

      if (data.imageUrl) {
        setGeneratedImage(data.imageUrl);
        toast({
          title: "Image generated!",
          description: "Your AI-generated image is ready to use.",
        });
      } else {
        throw new Error("No image generated");
      }
    } catch (error: any) {
      toast({
        title: "Error generating image",
        description: error.message || "Failed to generate image",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Upload image file
  const uploadImage = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      // Validate file
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please upload an image file",
          variant: "destructive",
        });
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please upload an image smaller than 5MB",
          variant: "destructive",
        });
        return;
      }

      setIsUploadingImage(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error("User not authenticated");

        const fileExt = file.name.split('.').pop();
        const fileName = `${user.id}/${Date.now()}.${fileExt}`;

        const { data, error } = await supabase.storage
          .from('user-images')
          .upload(fileName, file);

        if (error) throw error;

        const { data: { publicUrl } } = supabase.storage
          .from('user-images')
          .getPublicUrl(fileName);

        setUploadedImage(publicUrl);
        toast({
          title: "Image uploaded!",
          description: "Your image is ready to use in your posts.",
        });
      } catch (error: any) {
        toast({
          title: "Error uploading image",
          description: error.message || "Failed to upload image",
          variant: "destructive",
        });
      } finally {
        setIsUploadingImage(false);
      }
    };
    input.click();
  };

  // Publish post
  const publishPost = async () => {
    if (!generatedContent.trim()) {
      toast({
        title: "No content to publish",
        description: "Please generate or write some content first",
        variant: "destructive",
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "No platforms selected",
        description: "Please select at least one platform to publish to",
        variant: "destructive",
      });
      return;
    }

    setIsPosting(true);
    try {
      const platformsData = selectedPlatforms.map(platform => ({ platform }));
      const finalImage = generatedImage || uploadedImage;

      const results = await postToMultiplePlatforms(
        generatedContent,
        platformsData,
        finalImage
      );

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        toast({
          title: "Post published successfully!",
          description: `Published to ${successCount} platform${successCount > 1 ? 's' : ''}${failCount > 0 ? `, ${failCount} failed` : ''}`,
        });

        // Reset form on success
        setPrompt("");
        setGeneratedContent("");
        setSelectedPlatforms([]);
        setGeneratedImage(null);
        setUploadedImage(null);
        setIncludeImage(false);
        setSchedulePost(false);
        setScheduledTime("");
      } else {
        toast({
          title: "Publishing failed",
          description: "Failed to publish to any platforms. Please check your connections.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error publishing post",
        description: error.message || "Failed to publish post",
        variant: "destructive",
      });
    } finally {
      setIsPosting(false);
    }
  };

  // Generate content with AI
  const generateContent = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Please enter a prompt",
        description: "Describe what you want to create a post about",
        variant: "destructive",
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "Please select platforms",
        description: "Choose at least one platform to generate content for",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      const hasTwitter = selectedPlatforms.includes('twitter');
      const maxLength = hasTwitter ? 280 : 500;

      const { data, error } = await supabase.functions.invoke('generate-content', {
        body: {
          prompt: `Create a single ${selectedTone} social media post about: ${prompt}. ${hasTwitter ? 'Keep it under 280 characters for Twitter.' : 'Keep it concise and engaging.'} Return ONLY the post content, no instructions or multiple options.`,
          tone: selectedTone,
          type: 'text',
          maxLength,
          singlePost: true
        }
      });

      if (error) throw error;

      let content = data?.content || data?.generatedText || data?.message || "Check out this amazing content!";
      content = cleanAIGeneratedContent(content);

      setGeneratedContent(content);
      
      toast({
        title: "Content generated!",
        description: "Your AI-generated content is ready. You can edit it before posting.",
      });
    } catch (error: any) {
      console.error('Content generation error:', error);
      
      // Fallback content
      const fallbackContent = `${prompt}\n\nWe're excited to share this with our community! 🚀\n\n#content #social`;
      setGeneratedContent(fallbackContent);
      
      toast({
        title: "Content generated!",
        description: "Generated content using smart templates. You can edit it as needed.",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Create Amazing Content
              </h1>
              <p className="text-gray-600 text-lg">Let AI create engaging posts for your audience</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Content Creation */}
          <div className="lg:col-span-2 space-y-6">
            {/* Prompt Input */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Edit3 className="h-5 w-5 text-purple-600" />
                  <span>What do you want to post about?</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="Describe your post idea... (e.g., 'Announcing our new product launch', 'Tips for remote work productivity', 'Behind the scenes of our team')"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[120px] bg-white border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-xl resize-none"
                />
                
                {/* Tone Selection */}
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-gray-700">Choose your tone:</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {toneOptions.map((tone) => (
                      <button
                        key={tone.id}
                        onClick={() => setSelectedTone(tone.id)}
                        className={`p-3 rounded-xl border-2 transition-all text-left ${
                          selectedTone === tone.id
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200 hover:border-purple-300 bg-white'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{tone.emoji}</span>
                          <div>
                            <div className="font-medium text-sm">{tone.name}</div>
                            <div className="text-xs text-gray-500">{tone.description}</div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                <Button
                  onClick={generateContent}
                  disabled={isGenerating || !prompt.trim()}
                  className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin mr-2" />
                      Generating Content...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-5 h-5 mr-2" />
                      Generate Content
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Generated Content Preview */}
            {generatedContent && (
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Eye className="h-5 w-5 text-green-600" />
                    <span>Generated Content</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    value={generatedContent}
                    onChange={(e) => setGeneratedContent(e.target.value)}
                    className="min-h-[150px] bg-white border-gray-200 focus:border-green-500 focus:ring-green-500 rounded-xl resize-none"
                    placeholder="Your generated content will appear here..."
                  />

                  {/* Character count for selected platforms */}
                  <div className="flex flex-wrap gap-2">
                    {selectedPlatforms.map(platformId => {
                      const platform = platforms.find(p => p.id === platformId);
                      if (!platform) return null;

                      const charCount = generatedContent.length;
                      const isOverLimit = charCount > platform.limit;

                      return (
                        <Badge
                          key={platformId}
                          variant={isOverLimit ? "destructive" : "secondary"}
                          className="text-xs"
                        >
                          {platform.name}: {charCount}/{platform.limit}
                        </Badge>
                      );
                    })}
                  </div>

                  {/* Image Options */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={includeImage}
                        onCheckedChange={setIncludeImage}
                        id="include-image"
                      />
                      <label htmlFor="include-image" className="text-sm font-medium">
                        Include image with post
                      </label>
                    </div>

                    {includeImage && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Button
                          variant="outline"
                          onClick={generateImage}
                          disabled={isGeneratingImage}
                          className="h-24 border-dashed border-2 border-purple-300 hover:border-purple-500 bg-purple-50 hover:bg-purple-100"
                        >
                          {isGeneratingImage ? (
                            <Loader2 className="w-6 h-6 animate-spin" />
                          ) : (
                            <div className="text-center">
                              <Sparkles className="w-6 h-6 mx-auto mb-1 text-purple-600" />
                              <span className="text-sm font-medium">Generate AI Image</span>
                            </div>
                          )}
                        </Button>

                        <Button
                          variant="outline"
                          onClick={uploadImage}
                          disabled={isUploadingImage}
                          className="h-24 border-dashed border-2 border-blue-300 hover:border-blue-500 bg-blue-50 hover:bg-blue-100"
                        >
                          {isUploadingImage ? (
                            <Loader2 className="w-6 h-6 animate-spin" />
                          ) : (
                            <div className="text-center">
                              <Upload className="w-6 h-6 mx-auto mb-1 text-blue-600" />
                              <span className="text-sm font-medium">Upload Image</span>
                            </div>
                          )}
                        </Button>
                      </div>
                    )}

                    {(generatedImage || uploadedImage) && (
                      <div className="mt-4">
                        <img
                          src={generatedImage || uploadedImage || ''}
                          alt="Post image"
                          className="w-full max-w-md rounded-xl shadow-lg"
                        />
                      </div>
                    )}
                  </div>

                  {/* Scheduling Options */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={schedulePost}
                        onCheckedChange={setSchedulePost}
                        id="schedule-post"
                      />
                      <label htmlFor="schedule-post" className="text-sm font-medium">
                        Schedule for later
                      </label>
                    </div>

                    {schedulePost && (
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <input
                          type="datetime-local"
                          value={scheduledTime}
                          onChange={(e) => setScheduledTime(e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                          min={new Date().toISOString().slice(0, 16)}
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Platform Selection & Actions */}
          <div className="space-y-6">
            {/* Platform Selection */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  <span>Select Platforms</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {platforms.map((platform) => {
                  const Icon = platform.icon;
                  const isConnected = connectedPlatforms.includes(platform.id);
                  const isSelected = selectedPlatforms.includes(platform.id);

                  return (
                    <button
                      key={platform.id}
                      onClick={() => togglePlatform(platform.id)}
                      disabled={!isConnected}
                      className={`w-full p-4 rounded-xl border-2 transition-all text-left ${
                        isSelected && isConnected
                          ? `border-${platform.textColor.split('-')[1]}-500 ${platform.bgLight}`
                          : isConnected
                          ? 'border-gray-200 hover:border-gray-300 bg-white'
                          : 'border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${isConnected ? platform.color : 'bg-gray-400'}`}>
                            <Icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <div className="font-semibold text-sm">{platform.name}</div>
                            <div className="text-xs text-gray-500">
                              {isConnected ? 'Connected' : 'Not connected'}
                            </div>
                          </div>
                        </div>

                        {isConnected && (
                          <div className={`w-5 h-5 rounded-full border-2 ${
                            isSelected
                              ? 'bg-blue-500 border-blue-500'
                              : 'border-gray-300'
                          }`}>
                            {isSelected && <CheckCircle className="w-5 h-5 text-white" />}
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}

                {connectedPlatforms.length === 0 && (
                  <div className="text-center py-6">
                    <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500 text-sm">No platforms connected</p>
                    <p className="text-gray-400 text-xs">Go to Settings to connect your accounts</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Publish Actions */}
            {generatedContent && selectedPlatforms.length > 0 && (
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Send className="h-5 w-5 text-green-600" />
                    <span>Ready to Publish</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                      Publishing to {selectedPlatforms.length} platform{selectedPlatforms.length > 1 ? 's' : ''}:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {selectedPlatforms.map(platformId => {
                        const platform = platforms.find(p => p.id === platformId);
                        if (!platform) return null;
                        const Icon = platform.icon;

                        return (
                          <Badge key={platformId} variant="secondary" className="flex items-center space-x-1">
                            <Icon className="w-3 h-3" />
                            <span>{platform.name}</span>
                          </Badge>
                        );
                      })}
                    </div>
                  </div>

                  <Separator />

                  <Button
                    onClick={publishPost}
                    disabled={isPosting}
                    className="w-full h-12 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all"
                  >
                    {isPosting ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin mr-2" />
                        Publishing...
                      </>
                    ) : schedulePost ? (
                      <>
                        <Clock className="w-5 h-5 mr-2" />
                        Schedule Post
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5 mr-2" />
                        Publish Now
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePostPage;
