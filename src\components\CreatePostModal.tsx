import React, { useState, useCallback } from "react";

interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenSettings?: () => void;
  onPostSuccess?: () => void;
}

const CreatePostModal: React.FC<CreatePostModalProps> = ({ isOpen, onClose }) => {
  const [prompt, setPrompt] = useState("");
  const [selectedTone, setSelectedTone] = useState("professional");
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState("");

  // Early return to prevent any rendering when modal is closed
  if (!isOpen) {
    return null;
  }

  const tones = [
    { id: "professional", name: "Professional", emoji: "💼" },
    { id: "casual", name: "Casual", emoji: "😊" },
    { id: "enthusiastic", name: "Enthusiastic", emoji: "🚀" }
  ];

  const platforms = [
    { id: "twitter", name: "Twitter", color: "bg-blue-500" },
    { id: "linkedin", name: "LinkedIn", color: "bg-blue-700" },
    { id: "facebook", name: "Facebook", color: "bg-blue-600" }
  ];

  const handlePromptChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    console.log("Typing:", e.target.value);
    setPrompt(e.target.value);
  }, []);

  const handleToneClick = useCallback((toneId: string) => {
    console.log("Tone selected:", toneId);
    setSelectedTone(toneId);
  }, []);

  const handlePlatformClick = useCallback((platformId: string) => {
    console.log("Platform clicked:", platformId);
    setSelectedPlatforms(prev => {
      if (prev.includes(platformId)) {
        return prev.filter(p => p !== platformId);
      } else {
        return [...prev, platformId];
      }
    });
  }, []);

  const handleGenerate = useCallback(() => {
    console.log("Generate clicked");
    if (!prompt.trim()) {
      alert("Please enter a prompt first");
      return;
    }
    setGeneratedContent(`Generated ${selectedTone} content for: ${prompt}`);
  }, [prompt, selectedTone]);

  const handlePublish = useCallback(() => {
    console.log("Publish clicked");
    alert(`Publishing to: ${selectedPlatforms.join(", ")}`);
    onClose();
  }, [selectedPlatforms, onClose]);

  return (
    <div style={{
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      zIndex: 10000
    }}>
      <div style={{
        backgroundColor: "white",
        borderRadius: "8px",
        padding: "24px",
        width: "90%",
        maxWidth: "600px",
        maxHeight: "90vh",
        overflow: "auto"
      }}>
        {/* Header */}
        <div style={{ 
          display: "flex", 
          justifyContent: "space-between", 
          alignItems: "center", 
          marginBottom: "20px",
          borderBottom: "1px solid #e5e7eb",
          paddingBottom: "16px"
        }}>
          <h2 style={{ margin: 0, fontSize: "24px", fontWeight: "bold" }}>Create Post</h2>
          <button 
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              padding: "4px"
            }}
          >
            ×
          </button>
        </div>

        {/* Debug Info */}
        <div style={{
          backgroundColor: "#f3f4f6",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "16px",
          fontSize: "12px"
        }}>
          <div>Prompt: "{prompt}"</div>
          <div>Tone: {selectedTone}</div>
          <div>Platforms: [{selectedPlatforms.join(", ")}]</div>
        </div>

        {/* Prompt Input */}
        <div style={{ marginBottom: "16px" }}>
          <label style={{ 
            display: "block", 
            fontWeight: "bold", 
            marginBottom: "8px" 
          }}>
            What do you want to post about?
          </label>
          <textarea
            value={prompt}
            onChange={handlePromptChange}
            placeholder="Type your post idea here..."
            style={{
              width: "100%",
              height: "80px",
              padding: "8px",
              border: "1px solid #d1d5db",
              borderRadius: "4px",
              fontSize: "14px",
              resize: "none",
              outline: "none"
            }}
            onFocus={(e) => {
              e.target.style.borderColor = "#8b5cf6";
            }}
            onBlur={(e) => {
              e.target.style.borderColor = "#d1d5db";
            }}
          />
        </div>

        {/* Tone Selection */}
        <div style={{ marginBottom: "16px" }}>
          <label style={{ 
            display: "block", 
            fontWeight: "bold", 
            marginBottom: "8px" 
          }}>
            Choose tone:
          </label>
          <div style={{ 
            display: "grid", 
            gridTemplateColumns: "repeat(3, 1fr)", 
            gap: "8px" 
          }}>
            {tones.map((tone) => (
              <button
                key={tone.id}
                onClick={() => handleToneClick(tone.id)}
                style={{
                  padding: "8px",
                  border: selectedTone === tone.id ? "2px solid #8b5cf6" : "1px solid #d1d5db",
                  borderRadius: "4px",
                  backgroundColor: selectedTone === tone.id ? "#f3f4f6" : "white",
                  cursor: "pointer",
                  fontSize: "12px"
                }}
              >
                {tone.emoji} {tone.name}
              </button>
            ))}
          </div>
        </div>

        {/* Platform Selection */}
        <div style={{ marginBottom: "16px" }}>
          <label style={{ 
            display: "block", 
            fontWeight: "bold", 
            marginBottom: "8px" 
          }}>
            Select platforms:
          </label>
          <div style={{ 
            display: "grid", 
            gridTemplateColumns: "repeat(3, 1fr)", 
            gap: "8px" 
          }}>
            {platforms.map((platform) => (
              <button
                key={platform.id}
                onClick={() => handlePlatformClick(platform.id)}
                style={{
                  padding: "8px",
                  border: selectedPlatforms.includes(platform.id) ? "2px solid #3b82f6" : "1px solid #d1d5db",
                  borderRadius: "4px",
                  backgroundColor: selectedPlatforms.includes(platform.id) ? "#dbeafe" : "white",
                  cursor: "pointer",
                  fontSize: "12px"
                }}
              >
                {platform.name}
              </button>
            ))}
          </div>
        </div>

        {/* Generate Button */}
        <button
          onClick={handleGenerate}
          disabled={!prompt.trim()}
          style={{
            width: "100%",
            padding: "12px",
            backgroundColor: prompt.trim() ? "#8b5cf6" : "#d1d5db",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: prompt.trim() ? "pointer" : "not-allowed",
            fontSize: "16px",
            fontWeight: "bold",
            marginBottom: "16px"
          }}
        >
          Generate Content
        </button>

        {/* Generated Content */}
        {generatedContent && (
          <div style={{ marginBottom: "16px" }}>
            <label style={{ 
              display: "block", 
              fontWeight: "bold", 
              marginBottom: "8px" 
            }}>
              Generated content:
            </label>
            <textarea
              value={generatedContent}
              onChange={(e) => setGeneratedContent(e.target.value)}
              style={{
                width: "100%",
                height: "80px",
                padding: "8px",
                border: "1px solid #d1d5db",
                borderRadius: "4px",
                fontSize: "14px",
                resize: "none",
                outline: "none"
              }}
            />
          </div>
        )}

        {/* Publish Button */}
        {generatedContent && selectedPlatforms.length > 0 && (
          <button
            onClick={handlePublish}
            style={{
              width: "100%",
              padding: "12px",
              backgroundColor: "#10b981",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold",
              marginBottom: "16px"
            }}
          >
            Publish to {selectedPlatforms.length} Platform{selectedPlatforms.length > 1 ? 's' : ''}
          </button>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          style={{
            width: "100%",
            padding: "8px",
            backgroundColor: "#6b7280",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            fontSize: "14px"
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default CreatePostModal;
