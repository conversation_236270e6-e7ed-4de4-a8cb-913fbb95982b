import React from "react";

interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenSettings?: () => void;
  onPostSuccess?: () => void;
}

const CreatePostModal: React.FC<CreatePostModalProps> = ({ isOpen, onClose }) => {
  // Simple state without hooks to prevent re-render issues
  const [state, setState] = React.useState({
    prompt: "",
    tone: "professional",
    platforms: [] as string[],
    content: "",
    step: 1
  });

  // Don't render anything if modal is closed
  if (!isOpen) return null;

  const tones = [
    { id: "professional", name: "Professional", emoji: "💼" },
    { id: "casual", name: "Casual", emoji: "😊" },
    { id: "creative", name: "Creative", emoji: "🎨" }
  ];

  const platforms = [
    { id: "twitter", name: "Twitter", color: "#1DA1F2" },
    { id: "linkedin", name: "LinkedIn", color: "#0077B5" },
    { id: "facebook", name: "Facebook", color: "#1877F2" }
  ];

  const updateState = (updates: Partial<typeof state>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const handleGenerate = () => {
    if (!state.prompt.trim()) {
      alert("Please enter a prompt");
      return;
    }
    updateState({
      content: `Generated ${state.tone} content: ${state.prompt}`,
      step: 2
    });
  };

  const handlePublish = () => {
    if (state.platforms.length === 0) {
      alert("Please select platforms");
      return;
    }
    alert(`Publishing to: ${state.platforms.join(", ")}`);
    onClose();
  };

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 999999,
        padding: "20px"
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "16px",
          width: "100%",
          maxWidth: "600px",
          maxHeight: "90vh",
          overflow: "auto",
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header - Ockoya Style */}
        <div style={{
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          padding: "24px",
          borderRadius: "16px 16px 0 0",
          color: "white"
        }}>
          <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <div>
              <h2 style={{ margin: 0, fontSize: "24px", fontWeight: "600" }}>
                Create Post
              </h2>
              <p style={{ margin: "4px 0 0 0", opacity: 0.9, fontSize: "14px" }}>
                Generate engaging content for your audience
              </p>
            </div>
            <button
              onClick={onClose}
              style={{
                background: "rgba(255, 255, 255, 0.2)",
                border: "none",
                borderRadius: "8px",
                padding: "8px",
                color: "white",
                cursor: "pointer",
                fontSize: "18px"
              }}
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div style={{ padding: "24px" }}>
          {/* Step 1: Input */}
          {state.step === 1 && (
            <div>
              {/* Prompt Input */}
              <div style={{ marginBottom: "20px" }}>
                <label style={{
                  display: "block",
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#374151",
                  marginBottom: "8px"
                }}>
                  What would you like to post about?
                </label>
                <textarea
                  value={state.prompt}
                  onChange={(e) => updateState({ prompt: e.target.value })}
                  placeholder="Describe your post idea..."
                  style={{
                    width: "100%",
                    minHeight: "100px",
                    padding: "12px",
                    border: "2px solid #e5e7eb",
                    borderRadius: "12px",
                    fontSize: "14px",
                    fontFamily: "inherit",
                    resize: "vertical",
                    outline: "none",
                    transition: "border-color 0.2s"
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "#667eea";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "#e5e7eb";
                  }}
                />
              </div>

              {/* Tone Selection */}
              <div style={{ marginBottom: "20px" }}>
                <label style={{
                  display: "block",
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#374151",
                  marginBottom: "12px"
                }}>
                  Choose your tone
                </label>
                <div style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: "12px"
                }}>
                  {tones.map((tone) => (
                    <button
                      key={tone.id}
                      onClick={() => updateState({ tone: tone.id })}
                      style={{
                        padding: "12px",
                        border: state.tone === tone.id ? "2px solid #667eea" : "2px solid #e5e7eb",
                        borderRadius: "12px",
                        backgroundColor: state.tone === tone.id ? "#f0f4ff" : "white",
                        cursor: "pointer",
                        transition: "all 0.2s",
                        fontSize: "14px",
                        fontWeight: "500"
                      }}
                    >
                      <div>{tone.emoji}</div>
                      <div style={{ marginTop: "4px" }}>{tone.name}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Platform Selection */}
              <div style={{ marginBottom: "24px" }}>
                <label style={{
                  display: "block",
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#374151",
                  marginBottom: "12px"
                }}>
                  Select platforms
                </label>
                <div style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: "12px"
                }}>
                  {platforms.map((platform) => (
                    <button
                      key={platform.id}
                      onClick={() => {
                        const newPlatforms = state.platforms.includes(platform.id)
                          ? state.platforms.filter(p => p !== platform.id)
                          : [...state.platforms, platform.id];
                        updateState({ platforms: newPlatforms });
                      }}
                      style={{
                        padding: "12px",
                        border: state.platforms.includes(platform.id) ? `2px solid ${platform.color}` : "2px solid #e5e7eb",
                        borderRadius: "12px",
                        backgroundColor: state.platforms.includes(platform.id) ? `${platform.color}15` : "white",
                        cursor: "pointer",
                        transition: "all 0.2s",
                        fontSize: "14px",
                        fontWeight: "500",
                        color: state.platforms.includes(platform.id) ? platform.color : "#374151"
                      }}
                    >
                      {platform.name}
                      {state.platforms.includes(platform.id) && (
                        <div style={{ marginTop: "4px", fontSize: "12px" }}>✓ Selected</div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={!state.prompt.trim()}
                style={{
                  width: "100%",
                  padding: "16px",
                  background: state.prompt.trim()
                    ? "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                    : "#d1d5db",
                  color: "white",
                  border: "none",
                  borderRadius: "12px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: state.prompt.trim() ? "pointer" : "not-allowed",
                  transition: "all 0.2s"
                }}
              >
                Generate Content ✨
              </button>
            </div>
          )}

          {/* Step 2: Review & Publish */}
          {state.step === 2 && (
            <div>
              <div style={{ marginBottom: "20px" }}>
                <label style={{
                  display: "block",
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#374151",
                  marginBottom: "8px"
                }}>
                  Generated Content
                </label>
                <textarea
                  value={state.content}
                  onChange={(e) => updateState({ content: e.target.value })}
                  style={{
                    width: "100%",
                    minHeight: "120px",
                    padding: "12px",
                    border: "2px solid #10b981",
                    borderRadius: "12px",
                    fontSize: "14px",
                    fontFamily: "inherit",
                    resize: "vertical",
                    outline: "none",
                    backgroundColor: "#f0fdf4"
                  }}
                />
              </div>

              <div style={{ display: "flex", gap: "12px" }}>
                <button
                  onClick={() => updateState({ step: 1 })}
                  style={{
                    flex: 1,
                    padding: "12px",
                    background: "white",
                    color: "#6b7280",
                    border: "2px solid #e5e7eb",
                    borderRadius: "12px",
                    fontSize: "14px",
                    fontWeight: "600",
                    cursor: "pointer"
                  }}
                >
                  ← Back
                </button>
                <button
                  onClick={handlePublish}
                  style={{
                    flex: 2,
                    padding: "12px",
                    background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                    color: "white",
                    border: "none",
                    borderRadius: "12px",
                    fontSize: "14px",
                    fontWeight: "600",
                    cursor: "pointer"
                  }}
                >
                  Publish to {state.platforms.length} Platform{state.platforms.length !== 1 ? 's' : ''} 🚀
                </button>
              </div>
            </div>
          )}
      </div>
    </div>
  );
};

export default CreatePostModal;
