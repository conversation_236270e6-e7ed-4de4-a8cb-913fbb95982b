import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Sparkles,
  Wand2,
  Facebook,
  Instagram,
  Linkedin,
  Twitter,
  Send,
  CheckCircle,
  Loader2,
  X,
  Image,
  Upload,
  Download
} from "lucide-react";
import { FaReddit } from "react-icons/fa";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useSocialMedia } from "@/hooks/useSocialMedia";

interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenSettings?: () => void;
  onPostSuccess?: () => void;
}

const CreatePostModal: React.FC<CreatePostModalProps> = ({ isOpen, onClose, onOpenSettings, onPostSuccess }) => {
  const [prompt, setPrompt] = useState("");
  const [selectedTone, setSelectedTone] = useState("professional");
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPosting, setIsPosting] = useState(false);

  // Image-related state
  const [imageSource, setImageSource] = useState<'none' | 'upload' | 'generate'>('none');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);

  const { toast } = useToast();
  const { connections, postToMultiplePlatforms, checkConnectionStatus } = useSocialMedia();

  // Platform configurations
  const platforms = [
    { id: "twitter", name: "Twitter", icon: Twitter, color: "bg-sky-500", limit: 280 },
    { id: "facebook", name: "Facebook", icon: Facebook, color: "bg-blue-600", limit: 63206 },
    { id: "instagram", name: "Instagram", icon: Instagram, color: "bg-pink-500", limit: 2200 },
    { id: "linkedin", name: "LinkedIn", icon: Linkedin, color: "bg-blue-700", limit: 3000 },
    { id: "reddit", name: "Reddit", icon: FaReddit, color: "bg-orange-500", limit: 40000 },
  ];

  // Tone options
  const toneOptions = [
    { id: "professional", name: "Professional", emoji: "💼" },
    { id: "casual", name: "Casual", emoji: "😊" },
    { id: "enthusiastic", name: "Enthusiastic", emoji: "🚀" },
    { id: "informative", name: "Informative", emoji: "📚" },
    { id: "humorous", name: "Humorous", emoji: "😄" },
    { id: "inspirational", name: "Inspirational", emoji: "✨" },
  ];

  // Get connected platforms
  const connectedPlatforms = connections
    .filter(conn => conn.isConnected)
    .map(conn => conn.platform);

  useEffect(() => {
    if (isOpen) {
      checkConnectionStatus();
      setPrompt("");
      setGeneratedContent("");
      setSelectedPlatforms([]);
      setImageSource('none');
      setUploadedImage(null);
      setGeneratedImage(null);
    }
  }, [isOpen, checkConnectionStatus]);

  // Platform selection handler
  const togglePlatform = (platformId: string) => {
    if (!connectedPlatforms.includes(platformId)) {
      toast({
        title: "Platform not connected",
        description: `Please connect your ${platformId} account in Settings first.`,
        variant: "destructive",
      });
      return;
    }

    setSelectedPlatforms(prev =>
      prev.includes(platformId)
        ? prev.filter(p => p !== platformId)
        : [...prev, platformId]
    );
  };

  // Image upload handler
  const handleImageUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file (PNG, JPG, GIF, etc.)",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB",
        variant: "destructive",
      });
      return;
    }

    setIsUploadingImage(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('user-images')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('user-images')
        .getPublicUrl(fileName);

      setUploadedImage(publicUrl);
      setGeneratedImage(null); // Clear generated image if uploading

      toast({
        title: "Image uploaded successfully!",
        description: "Your image is ready to be included with your post.",
      });
    } catch (error: any) {
      console.error('Image upload error:', error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // AI image generation handler
  const generateImage = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Please enter a prompt first",
        description: "Describe your post content to generate a relevant image",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingImage(true);
    try {
      const imagePrompt = `Create a professional social media image for: ${prompt}. Make it visually appealing, modern, and suitable for social media platforms.`;

      const { data, error } = await supabase.functions.invoke('generate-content', {
        body: {
          prompt: imagePrompt,
          type: 'image'
        }
      });

      if (error) throw error;

      if (data?.imageUrl) {
        setGeneratedImage(data.imageUrl);
        setUploadedImage(null); // Clear uploaded image if generating

        toast({
          title: "Image generated successfully!",
          description: "Your AI-generated image is ready to be included with your post.",
        });
      } else {
        throw new Error("No image URL returned from AI generation");
      }
    } catch (error: any) {
      console.error('Image generation error:', error);
      toast({
        title: "Image generation failed",
        description: error.message || "Failed to generate image. Please try again or upload your own image.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Generate content with AI
  const generateContent = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Please enter a prompt",
        description: "Describe what you want to create a post about",
        variant: "destructive",
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "Please select platforms",
        description: "Choose at least one platform to generate content for",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      const hasTwitter = selectedPlatforms.includes('twitter');
      const maxLength = hasTwitter ? 280 : 500;

      const { data, error } = await supabase.functions.invoke('generate-content', {
        body: {
          prompt: `Create a single ${selectedTone} social media post about: ${prompt}. ${hasTwitter ? 'Keep it under 280 characters for Twitter.' : 'Keep it concise and engaging.'} Return ONLY the post content, no instructions or multiple options.`,
          tone: selectedTone,
          type: 'text',
          maxLength,
          singlePost: true
        }
      });

      if (error) throw error;

      let content = data?.content || data?.generatedText || data?.message || "Check out this amazing content!";

      // Clean up AI-generated content
      content = content.trim();
      content = content.replace(/^(Sure!|Here are|Here's|I'll help|Let me|I can help).*?(\n|:)/i, '').trim();
      content = content.replace(/^.*social media content ideas.*?(\n|:)/i, '').trim();

      // If content has numbered list, extract ONLY the first item
      if (content.match(/^\d+\./m)) {
        const lines = content.split('\n');
        for (const line of lines) {
          if (line.match(/^1\./)) {
            content = line.replace(/^1\.\s*/, '').trim();
            break;
          }
        }
      }

      setGeneratedContent(content);

      toast({
        title: "Content generated!",
        description: "Your AI-generated content is ready. You can edit it before posting.",
      });
    } catch (error: any) {
      console.error('Content generation error:', error);

      // Fallback content
      const fallbackContent = `${prompt}\n\nWe're excited to share this with our community! 🚀\n\n#content #social`;
      setGeneratedContent(fallbackContent);

      toast({
        title: "Content generated!",
        description: "Generated content using smart templates. You can edit it as needed.",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Publish post
  const publishPost = async () => {
    if (!generatedContent.trim()) {
      toast({
        title: "No content to publish",
        description: "Please generate or write some content first",
        variant: "destructive",
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "No platforms selected",
        description: "Please select at least one platform to publish to",
        variant: "destructive",
      });
      return;
    }

    setIsPosting(true);
    try {
      const platformsData = selectedPlatforms.map(platform => ({ platform }));
      const imageToInclude = uploadedImage || generatedImage || undefined;

      const results = await postToMultiplePlatforms(
        generatedContent,
        platformsData,
        imageToInclude
      );

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        toast({
          title: "Post published successfully!",
          description: `Published to ${successCount} platform${successCount > 1 ? 's' : ''}${failCount > 0 ? `, ${failCount} failed` : ''}`,
        });

        if (onPostSuccess) {
          onPostSuccess();
        }

        onClose();
      } else {
        toast({
          title: "Publishing failed",
          description: "Failed to publish to any platforms. Please check your connections.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error publishing post",
        description: error.message || "Failed to publish post",
        variant: "destructive",
      });
    } finally {
      setIsPosting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <DialogHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6 -m-6 mb-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-xl">
                <Sparkles className="h-6 w-6" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold">
                  Create Amazing Content
                </DialogTitle>
                <p className="text-purple-100 mt-1">Let AI create engaging posts for your audience</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-full transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Content Input Section */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                What do you want to post about? *
              </label>
              <Textarea
                placeholder="Describe your post idea... (e.g., 'Announcing our new product launch', 'Tips for remote work productivity')"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[100px] resize-none"
              />
            </div>

            {/* Tone Selection */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-3">Choose your tone:</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {toneOptions.map((tone) => (
                  <button
                    key={tone.id}
                    type="button"
                    onClick={() => setSelectedTone(tone.id)}
                    className={`p-3 rounded-xl border-2 transition-all text-left ${
                      selectedTone === tone.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-300 bg-white'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{tone.emoji}</span>
                      <span className="font-medium text-sm">{tone.name}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Image Options Section */}
            <div className="border-t pt-4">
              <label className="block text-sm font-semibold text-gray-700 mb-3">Add an Image (Optional)</label>

              {/* Image Source Selection */}
              <div className="grid grid-cols-3 gap-2 mb-4">
                <Button
                  type="button"
                  variant={imageSource === 'none' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    setImageSource('none');
                    setUploadedImage(null);
                    setGeneratedImage(null);
                  }}
                  className="text-xs"
                >
                  No Image
                </Button>
                <Button
                  type="button"
                  variant={imageSource === 'upload' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setImageSource('upload')}
                  className="text-xs"
                >
                  📁 Upload
                </Button>
                <Button
                  type="button"
                  variant={imageSource === 'generate' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setImageSource('generate')}
                  className="text-xs"
                >
                  🤖 AI Generate
                </Button>
              </div>

              {/* Image Upload */}
              {imageSource === 'upload' && (
                <div className="space-y-3">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImageUpload(file);
                      }}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <div className="flex flex-col items-center space-y-2">
                        <Upload className="w-8 h-8 text-gray-400" />
                        <div className="text-sm text-gray-600">
                          <span className="font-medium text-purple-600">Click to upload</span> or drag and drop
                        </div>
                        <div className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</div>
                      </div>
                    </label>
                  </div>

                  {isUploadingImage && (
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Uploading image...</span>
                    </div>
                  )}

                  {uploadedImage && (
                    <div className="space-y-3">
                      <div className="relative">
                        <img
                          src={uploadedImage}
                          alt="Uploaded image"
                          className="w-full h-48 object-cover rounded-lg border"
                        />
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute top-2 right-2"
                          onClick={() => window.open(uploadedImage, '_blank')}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-green-600 text-center">✓ Image uploaded successfully</p>
                    </div>
                  )}
                </div>
              )}

              {/* AI Image Generation */}
              {imageSource === 'generate' && (
                <div className="space-y-3">
                  <Button
                    type="button"
                    onClick={generateImage}
                    disabled={isGeneratingImage || !prompt.trim()}
                    variant="outline"
                    className="w-full"
                  >
                    {isGeneratingImage ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        Generating Image...
                      </>
                    ) : (
                      <>
                        <Wand2 className="w-4 h-4 mr-2" />
                        Generate AI Image
                      </>
                    )}
                  </Button>

                  {generatedImage && (
                    <div className="space-y-3">
                      <div className="relative">
                        <img
                          src={generatedImage}
                          alt="Generated image"
                          className="w-full h-48 object-cover rounded-lg border"
                        />
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute top-2 right-2"
                          onClick={() => {
                            const link = document.createElement('a');
                            link.href = generatedImage;
                            link.download = 'generated-image.png';
                            link.click();
                          }}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-green-600 text-center">✓ AI image generated successfully</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Generate Content Button */}
            <Button
              type="button"
              onClick={generateContent}
              disabled={isGenerating || !prompt.trim()}
              className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl font-semibold"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin mr-2" />
                  Generating Content...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5 mr-2" />
                  Generate Content
                </>
              )}
            </Button>
          </div>

          {/* Generated Content Preview */}
          {generatedContent && (
            <div className="space-y-4">
              {/* Image Preview */}
              {(uploadedImage || generatedImage) && (
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Image Preview
                  </label>
                  <div className="relative">
                    <img
                      src={uploadedImage || generatedImage}
                      alt="Post image"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                    <div className="absolute top-2 left-2">
                      <Badge variant="secondary" className="text-xs">
                        {uploadedImage ? 'Uploaded' : 'AI Generated'}
                      </Badge>
                    </div>
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Generated Content
                </label>
                <Textarea
                  value={generatedContent}
                  onChange={(e) => setGeneratedContent(e.target.value)}
                  className="min-h-[120px] resize-none"
                  placeholder="Your generated content will appear here..."
                />
              </div>

              {/* Character count for selected platforms */}
              <div className="flex flex-wrap gap-2">
                {selectedPlatforms.map(platformId => {
                  const platform = platforms.find(p => p.id === platformId);
                  if (!platform) return null;

                  const charCount = generatedContent.length;
                  const isOverLimit = charCount > platform.limit;

                  return (
                    <Badge
                      key={platformId}
                      variant={isOverLimit ? "destructive" : "secondary"}
                      className="text-xs"
                    >
                      {platform.name}: {charCount}/{platform.limit}
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}

          {/* Platform Selection */}
          <div className="space-y-4">
            <label className="block text-sm font-semibold text-gray-700">
              Select Platforms
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {platforms.map((platform) => {
                const Icon = platform.icon;
                const isConnected = connectedPlatforms.includes(platform.id);
                const isSelected = selectedPlatforms.includes(platform.id);

                return (
                  <button
                    key={platform.id}
                    onClick={() => togglePlatform(platform.id)}
                    disabled={!isConnected}
                    className={`p-4 rounded-xl border-2 transition-all text-left ${
                      isSelected && isConnected
                        ? 'border-blue-500 bg-blue-50'
                        : isConnected
                        ? 'border-gray-200 hover:border-gray-300 bg-white'
                        : 'border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${isConnected ? platform.color : 'bg-gray-400'}`}>
                          <Icon className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold text-sm">{platform.name}</div>
                          <div className="text-xs text-gray-500">
                            {isConnected ? 'Connected' : 'Not connected'}
                          </div>
                        </div>
                      </div>

                      {isConnected && (
                        <div className={`w-5 h-5 rounded-full border-2 ${
                          isSelected
                            ? 'bg-blue-500 border-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {isSelected && <CheckCircle className="w-5 h-5 text-white" />}
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>

            {connectedPlatforms.length === 0 && (
              <div className="text-center py-6">
                <p className="text-gray-500 text-sm">No platforms connected</p>
                <Button
                  variant="outline"
                  onClick={onOpenSettings}
                  className="mt-2"
                >
                  Go to Settings
                </Button>
              </div>
            )}
          </div>

          {/* Publish Button */}
          {generatedContent && selectedPlatforms.length > 0 && (
            <div className="pt-4 border-t">
              <Button
                onClick={publishPost}
                disabled={isPosting}
                className="w-full h-12 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-semibold"
              >
                {isPosting ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin mr-2" />
                    Publishing...
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5 mr-2" />
                    Publish to {selectedPlatforms.length} Platform{selectedPlatforms.length > 1 ? 's' : ''}
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePostModal;
