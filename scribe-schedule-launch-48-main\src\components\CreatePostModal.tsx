import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Wand2,
  Facebook,
  Instagram,
  Linkedin,
  Twitter,
  Send,
  CheckCircle,
  Loader2,
  X,
  Upload,
  Download
} from "lucide-react";
import { FaReddit } from "react-icons/fa";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useSocialMedia } from "@/hooks/useSocialMedia";

interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenSettings?: () => void;
  onPostSuccess?: () => void;
}

const CreatePostModal: React.FC<CreatePostModalProps> = ({ isOpen, onClose, onOpenSettings, onPostSuccess }) => {
  // Simple state with explicit setters
  const [prompt, setPrompt] = useState("");
  const [selectedTone, setSelectedTone] = useState("professional");
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [imageSource, setImageSource] = useState<'none' | 'upload' | 'generate'>('none');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);

  const { toast } = useToast();
  const { connections, postToMultiplePlatforms, checkConnectionStatus } = useSocialMedia();

  // Debug function to test state changes
  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    console.log("Prompt changing to:", e.target.value);
    setPrompt(e.target.value);
  };

  const handlePlatformClick = (platformId: string) => {
    console.log("Platform clicked:", platformId);
    const isConnected = connectedPlatforms.includes(platformId);
    if (!isConnected) {
      console.log("Platform not connected:", platformId);
      return;
    }

    setSelectedPlatforms(prev => {
      const newPlatforms = prev.includes(platformId)
        ? prev.filter(p => p !== platformId)
        : [...prev, platformId];
      console.log("New platforms:", newPlatforms);
      return newPlatforms;
    });
  };

  // Platform configurations
  const platforms = [
    { id: "twitter", name: "Twitter", icon: Twitter, color: "bg-sky-500", limit: 280 },
    { id: "facebook", name: "Facebook", icon: Facebook, color: "bg-blue-600", limit: 63206 },
    { id: "instagram", name: "Instagram", icon: Instagram, color: "bg-pink-500", limit: 2200 },
    { id: "linkedin", name: "LinkedIn", icon: Linkedin, color: "bg-blue-700", limit: 3000 },
    { id: "reddit", name: "Reddit", icon: FaReddit, color: "bg-orange-500", limit: 40000 },
  ];

  // Tone options
  const toneOptions = [
    { id: "professional", name: "Professional", emoji: "💼" },
    { id: "casual", name: "Casual", emoji: "😊" },
    { id: "enthusiastic", name: "Enthusiastic", emoji: "🚀" },
    { id: "informative", name: "Informative", emoji: "📚" },
    { id: "humorous", name: "Humorous", emoji: "😄" },
    { id: "inspirational", name: "Inspirational", emoji: "✨" },
  ];

  // Get connected platforms
  const connectedPlatforms = connections
    .filter(conn => conn.isConnected)
    .map(conn => conn.platform);

  useEffect(() => {
    if (isOpen) {
      checkConnectionStatus();
      setPrompt("");
      setGeneratedContent("");
      setSelectedPlatforms([]);
      setImageSource('none');
      setUploadedImage(null);
      setGeneratedImage(null);
    }
  }, [isOpen, checkConnectionStatus]);

  // Platform selection handler
  const togglePlatform = (platformId: string) => {
    if (!connectedPlatforms.includes(platformId)) {
      toast({
        title: "Platform not connected",
        description: `Please connect your ${platformId} account in Settings first.`,
        variant: "destructive",
      });
      return;
    }

    setSelectedPlatforms(prev =>
      prev.includes(platformId)
        ? prev.filter(p => p !== platformId)
        : [...prev, platformId]
    );
  };

  // Image upload handler
  const handleImageUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file (PNG, JPG, GIF, etc.)",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB",
        variant: "destructive",
      });
      return;
    }

    setIsUploadingImage(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('user-images')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('user-images')
        .getPublicUrl(fileName);

      setUploadedImage(publicUrl);
      setGeneratedImage(null); // Clear generated image if uploading

      toast({
        title: "Image uploaded successfully!",
        description: "Your image is ready to be included with your post.",
      });
    } catch (error: any) {
      console.error('Image upload error:', error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // AI image generation handler
  const generateImage = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Please enter a prompt first",
        description: "Describe your post content to generate a relevant image",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingImage(true);
    try {
      const imagePrompt = `Create a professional social media image for: ${prompt}. Make it visually appealing, modern, and suitable for social media platforms.`;

      const { data, error } = await supabase.functions.invoke('generate-content', {
        body: {
          prompt: imagePrompt,
          type: 'image'
        }
      });

      if (error) throw error;

      if (data?.imageUrl) {
        setGeneratedImage(data.imageUrl);
        setUploadedImage(null); // Clear uploaded image if generating

        toast({
          title: "Image generated successfully!",
          description: "Your AI-generated image is ready to be included with your post.",
        });
      } else {
        throw new Error("No image URL returned from AI generation");
      }
    } catch (error: any) {
      console.error('Image generation error:', error);
      toast({
        title: "Image generation failed",
        description: error.message || "Failed to generate image. Please try again or upload your own image.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Generate content with AI
  const generateContent = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Please enter a prompt",
        description: "Describe what you want to create a post about",
        variant: "destructive",
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "Please select platforms",
        description: "Choose at least one platform to generate content for",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      const hasTwitter = selectedPlatforms.includes('twitter');
      const maxLength = hasTwitter ? 280 : 500;

      const { data, error } = await supabase.functions.invoke('generate-content', {
        body: {
          prompt: `Create a single ${selectedTone} social media post about: ${prompt}. ${hasTwitter ? 'Keep it under 280 characters for Twitter.' : 'Keep it concise and engaging.'} Return ONLY the post content, no instructions or multiple options.`,
          tone: selectedTone,
          type: 'text',
          maxLength,
          singlePost: true
        }
      });

      if (error) throw error;

      let content = data?.content || data?.generatedText || data?.message || "Check out this amazing content!";

      // Clean up AI-generated content
      content = content.trim();
      content = content.replace(/^(Sure!|Here are|Here's|I'll help|Let me|I can help).*?(\n|:)/i, '').trim();
      content = content.replace(/^.*social media content ideas.*?(\n|:)/i, '').trim();

      // If content has numbered list, extract ONLY the first item
      if (content.match(/^\d+\./m)) {
        const lines = content.split('\n');
        for (const line of lines) {
          if (line.match(/^1\./)) {
            content = line.replace(/^1\.\s*/, '').trim();
            break;
          }
        }
      }

      setGeneratedContent(content);

      toast({
        title: "Content generated!",
        description: "Your AI-generated content is ready. You can edit it before posting.",
      });
    } catch (error: any) {
      console.error('Content generation error:', error);

      // Fallback content
      const fallbackContent = `${prompt}\n\nWe're excited to share this with our community! 🚀\n\n#content #social`;
      setGeneratedContent(fallbackContent);

      toast({
        title: "Content generated!",
        description: "Generated content using smart templates. You can edit it as needed.",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Publish post
  const publishPost = async () => {
    if (!generatedContent.trim()) {
      toast({
        title: "No content to publish",
        description: "Please generate or write some content first",
        variant: "destructive",
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "No platforms selected",
        description: "Please select at least one platform to publish to",
        variant: "destructive",
      });
      return;
    }

    setIsPosting(true);
    try {
      const platformsData = selectedPlatforms.map(platform => ({ platform }));
      const imageToInclude = uploadedImage || generatedImage || undefined;

      const results = await postToMultiplePlatforms(
        generatedContent,
        platformsData,
        imageToInclude
      );

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        toast({
          title: "Post published successfully!",
          description: `Published to ${successCount} platform${successCount > 1 ? 's' : ''}${failCount > 0 ? `, ${failCount} failed` : ''}`,
        });

        if (onPostSuccess) {
          onPostSuccess();
        }

        onClose();
      } else {
        toast({
          title: "Publishing failed",
          description: "Failed to publish to any platforms. Please check your connections.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error publishing post",
        description: error.message || "Failed to publish post",
        variant: "destructive",
      });
    } finally {
      setIsPosting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ zIndex: 9999 }}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
        style={{ zIndex: 9998 }}
      />

      {/* Modal */}
      <div
        className="relative bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto m-4"
        style={{ zIndex: 9999 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-purple-600 text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Create Post</h2>
            <button
              onClick={onClose}
              className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
              type="button"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Debug Info */}
          <div className="mb-4 p-2 bg-gray-100 rounded text-xs">
            <div>Prompt: "{prompt}"</div>
            <div>Tone: {selectedTone}</div>
            <div>Platforms: {selectedPlatforms.join(", ")}</div>
          </div>

          {/* Prompt Input */}
          <div className="mb-4">
            <label className="block text-sm font-bold mb-2">
              What do you want to post about?
            </label>
            <textarea
              placeholder="Type your post idea here..."
              value={prompt}
              onChange={handlePromptChange}
              className="w-full h-24 p-2 border border-gray-300 rounded focus:border-purple-500 focus:outline-none"
              style={{ resize: 'none' }}
            />
          </div>

          {/* Tone Selection */}
          <div className="mb-4">
            <label className="block text-sm font-bold mb-2">Choose tone:</label>
            <div className="grid grid-cols-3 gap-2">
              {toneOptions.map((tone) => (
                <button
                  key={tone.id}
                  type="button"
                  onClick={() => {
                    console.log("Tone clicked:", tone.id);
                    setSelectedTone(tone.id);
                  }}
                  className={`p-2 border rounded text-sm ${
                    selectedTone === tone.id
                      ? 'bg-purple-500 text-white border-purple-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-purple-300'
                  }`}
                >
                  {tone.emoji} {tone.name}
                </button>
              ))}
            </div>
          </div>

          {/* Platform Selection */}
          <div className="mb-4">
            <label className="block text-sm font-bold mb-2">Select platforms:</label>
            <div className="grid grid-cols-2 gap-2">
              {platforms.map((platform) => {
                const Icon = platform.icon;
                const isConnected = connectedPlatforms.includes(platform.id);
                const isSelected = selectedPlatforms.includes(platform.id);

                return (
                  <button
                    key={platform.id}
                    type="button"
                    onClick={() => handlePlatformClick(platform.id)}
                    disabled={!isConnected}
                    className={`p-2 border rounded text-sm flex items-center space-x-2 ${
                      isSelected && isConnected
                        ? 'bg-blue-500 text-white border-blue-500'
                        : isConnected
                        ? 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                        : 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{platform.name}</span>
                    {isSelected && <CheckCircle className="w-4 h-4" />}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Generate Button */}
          <div className="mb-4">
            <button
              type="button"
              onClick={() => {
                console.log("Generate clicked");
                if (!prompt.trim()) {
                  alert("Please enter a prompt first");
                  return;
                }
                setGeneratedContent(`Generated content for: ${prompt} (${selectedTone} tone)`);
              }}
              disabled={!prompt.trim()}
              className="w-full p-3 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Generate Content
            </button>
          </div>

          {/* Generated Content */}
          {generatedContent && (
            <div className="mb-4">
              <label className="block text-sm font-bold mb-2">Generated content:</label>
              <textarea
                value={generatedContent}
                onChange={(e) => setGeneratedContent(e.target.value)}
                className="w-full h-32 p-2 border border-gray-300 rounded focus:border-purple-500 focus:outline-none"
                style={{ resize: 'none' }}
              />
            </div>
          )}

          {/* Publish Button */}
          {generatedContent && selectedPlatforms.length > 0 && (
            <div className="mb-4">
              <button
                type="button"
                onClick={() => {
                  console.log("Publish clicked");
                  alert(`Publishing to: ${selectedPlatforms.join(", ")}`);
                  onClose();
                }}
                className="w-full p-3 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Publish to {selectedPlatforms.length} Platform{selectedPlatforms.length > 1 ? 's' : ''}
              </button>
            </div>
          )}

          {/* Close Button */}
          <div className="border-t pt-4">
            <button
              type="button"
              onClick={onClose}
              className="w-full p-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePostModal;
