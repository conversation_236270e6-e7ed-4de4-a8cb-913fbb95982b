<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>scribe-schedule-launch</title>
    <meta name="description" content="Lovable Generated Project" />
    <meta name="author" content="Lovable" />
    <meta property="og:title" content="scribe-schedule-launch" />
    <meta property="og:description" content="Lovable Generated Project" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- DNS Prefetch for social media analytics -->
    <link rel="dns-prefetch" href="//cdn.gpteng.co" />
    <link rel="dns-prefetch" href="//analytics.tiktok.com" />
    <link rel="dns-prefetch" href="//connect.facebook.net" />
    <link rel="dns-prefetch" href="//platform.linkedin.com" />
    <link rel="dns-prefetch" href="//www.redditstatic.com" />
    <link rel="dns-prefetch" href="//platform.twitter.com" />
    <link rel="dns-prefetch" href="//www.instagram.com" />

    <!-- Preload essential social media scripts with as="script" -->
    <link
      rel="preload"
      href="https://connect.facebook.net/en_US/fbevents.js"
      as="script"
    />
    <link
      rel="preload"
      href="https://platform.linkedin.com/in.js"
      as="script"
    />
    <link
      rel="preload"
      href="https://platform.twitter.com/widgets.js"
      as="script"
    />

    <!-- Error handling for third-party social media scripts -->
    <script>
      window.addEventListener("error", function (e) {
        const thirdPartyHosts = [
          "analytics.tiktok.com",
          "connect.facebook.net",
          "platform.linkedin.com",
          "platform.twitter.com",
          "www.redditstatic.com",
          "www.instagram.com",
        ];
        if (
          e.filename &&
          thirdPartyHosts.some((host) => e.filename.includes(host))
        ) {
          console.warn("Third-party script failed to load:", e.filename);
          return true; // Prevent breaking your app
        }
      });

      // Check preloads and monitor performance
      window.addEventListener("load", function () {
        setTimeout(() => {
          document.querySelectorAll('link[rel="preload"]').forEach((link) => {
            if (!link.hasAttribute("as")) {
              console.warn('Preload missing "as" attribute:', link.href);
            }
          });

          if ("performance" in window) {
            const navEntry = performance.getEntriesByType("navigation")[0];
            if (navEntry) {
              const loadTime = navEntry.loadEventEnd - navEntry.fetchStart;
              console.log("Page load time:", Math.round(loadTime), "ms");
            }
          }
        }, 1000);
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
